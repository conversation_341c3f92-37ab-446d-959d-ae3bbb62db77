<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true">

    <!-- Toolbar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/community_profile_header"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnBack"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="16dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <!-- User Info Section -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/userInfoSection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/divider1">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/ivUserAvatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/avatar"
            android:background="@drawable/circle_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvUserName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Lionel Messi"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@id/ivUserAvatar"
            app:layout_constraintTop_toTopOf="@id/ivUserAvatar" />

        <TextView
            android:id="@+id/tvUserStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No self introduction"
            android:textColor="#777777"
            android:textSize="14sp"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@id/ivUserAvatar"
            app:layout_constraintTop_toBottomOf="@id/tvUserName" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        app:layout_constraintTop_toBottomOf="@id/userInfoSection" />

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabSelectedTextColor="@android:color/black"
        app:tabIndicatorColor="@color/selectedicon"
        app:tabTextColor="#777777"
        app:layout_constraintTop_toBottomOf="@id/divider2">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/community_profile_your_posts" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/community_profile_liked_posts" />

    </com.google.android.material.tabs.TabLayout>

    <!-- Your Posts List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewYourPosts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:listitem="@layout/post_item"
        android:visibility="visible" />

    <!-- Liked Posts List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewLikedPosts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:listitem="@layout/post_item"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>