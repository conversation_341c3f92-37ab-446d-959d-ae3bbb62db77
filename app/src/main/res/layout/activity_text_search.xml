<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".presentation.feature.textsearch.TextSearchActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:src="@drawable/back_img" />


    </androidx.appcompat.widget.Toolbar>

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:padding="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <!-- Text Input Area -->
        <EditText
            android:id="@+id/editTextSearch"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:background="@drawable/border_round_12"
            android:hint="Type your question..."
            android:padding="15dp"
            android:textColorHint="@color/selectedicon"
            android:imeOptions="actionSearch"
            android:textSize="15sp"
            android:gravity="top|start"/>

        <!-- Thêm sau EditText -->
        <TextView
            android:id="@+id/tvInputError"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="4dp"
            android:text="Please enter at least 20 characters"
            android:textColor="@color/red"
            android:textSize="12sp"
            android:visibility="gone" />

        <!-- Search Button -->
        <Button
            android:id="@+id/btnSearch"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="30dp"
            android:text="Search"
            android:textFontWeight="400"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="10dp" />

        <!-- Loading Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:layout_marginTop="40dp"
            android:indeterminateTint="@color/selectedicon"
            android:visibility="gone" />

        <!-- Search Icon -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginTop="40dp"
            android:alpha="0.3"
            android:src="@drawable/ic_search_big"
            app:tint="@color/selectedicon" />



    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>