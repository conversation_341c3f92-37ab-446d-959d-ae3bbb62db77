<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@android:color/white">

    <!-- Top Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/white"
        android:elevation="1dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_back"
                android:contentDescription="Back"
                android:padding="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/post_comment_reply_header"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Content Area with Scroll -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/replyInputArea">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Parent Comment Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Parent Comment Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/ivParentUserAvatar"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/avatar"
                            android:background="@drawable/circle_background" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_marginStart="8dp">

                            <TextView
                                android:id="@+id/tvParentUserName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="User Name"
                                android:textColor="@android:color/black"
                                android:textStyle="bold"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tvParentTimeAgo"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="2 hours ago"
                                android:textColor="#777777"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Parent Comment Content -->
                    <TextView
                        android:id="@+id/tvParentContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="This is the parent comment content"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <!-- Parent Comment Images -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvParentImages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        tools:visibility="visible"
                        tools:listitem="@layout/item_comment_image" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Replies Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/post_comment_reply_all"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="8dp" />

            <!-- Replies List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvReplies"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/comment_item"
                tools:itemCount="3" />

            <!-- Selected Images Preview -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/post_comment_reply_image_selected"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone"
                android:id="@+id/tvSelectedImagesTitle" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvSelectedImages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:visibility="gone"
                tools:listitem="@layout/item_selected_image"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Reply Input Area (Fixed at Bottom) -->
    <LinearLayout
        android:id="@+id/replyInputArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:elevation="4dp"
        android:orientation="vertical"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Image Counter -->
        <TextView
            android:id="@+id/tvImageCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0/10"
            android:textSize="12sp"
            android:textColor="#AAAAAA"
            android:layout_gravity="end"
            android:layout_marginBottom="4dp"
            android:visibility="gone" />

        <!-- Input Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageButton
                android:id="@+id/btnAddImage"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_community_gallery"
                android:padding="8dp" />

            <EditText
                android:id="@+id/etReplyContent"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_weight="1"
                android:background="@drawable/bg_profile_field_border"
                android:hint="@string/post_comment_reply_hint"
                android:inputType="textMultiLine"
                android:maxLines="3"
                android:paddingHorizontal="12dp" />

            <ImageButton
                android:id="@+id/btnSendReply"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_send"
                android:padding="8dp" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>