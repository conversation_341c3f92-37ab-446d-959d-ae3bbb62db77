<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".presentation.feature.noteScene.NoteDetailActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/white"
        android:elevation="4dp"
        app:titleTextColor="@android:color/black"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Collaboration panel showing active users -->
    <LinearLayout
        android:id="@+id/collaboration_panel"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:background="#F5F5F5"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="8dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Collaborators: "
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp" />

        <com.example.app_music.presentation.feature.noteScene.views.UserPresenceView
            android:id="@+id/active_users_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/user_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp"
            tools:text="2" />
    </LinearLayout>

    <!-- Tools bar -->
    <LinearLayout
        android:id="@+id/layout_tools"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/collaboration_panel">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">
            <ImageButton
                android:id="@+id/button_hand"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Hand Tool"
                android:padding="8dp"
                android:src="@drawable/ic_hand" />

            <ImageButton
                android:id="@+id/button_pen"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Pen Tool"
                android:padding="8dp"
                android:src="@drawable/ic_pen" />

            <ImageButton
                android:id="@+id/button_eraser"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Eraser Tool"
                android:padding="8dp"
                android:src="@drawable/ic_eraser" />

            <ImageButton
                android:id="@+id/button_color"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Color Picker"
                android:padding="8dp"
                android:src="@drawable/ic_color_palette" />

            <ImageButton
                android:id="@+id/button_stroke_width"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Stroke Width"
                android:padding="8dp"
                android:src="@drawable/ic_stroke_width" />

        </LinearLayout>
    </LinearLayout>

    <!-- Page navigation bar -->
    <LinearLayout
        android:id="@+id/layout_page_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:background="#F5F5F5"
        android:elevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/layout_tools">

        <ImageButton
            android:id="@+id/button_prev_page"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back_left"
            android:contentDescription="Previous Page" />

        <TextView
            android:id="@+id/text_page_indicator"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Page 1 of 1"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:gravity="center" />

        <ImageButton
            android:id="@+id/button_next_page"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_right"
            android:contentDescription="Next Page" />

        <ImageButton
            android:id="@+id/button_add_page"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/plus_icon"
            android:contentDescription="Add Page" />
        <!-- In layout_page_navigation, add a delete button after the add button -->
        <ImageButton
            android:id="@+id/button_delete_page"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/delete_24dp_1f1f1f_fill0_wght400_grad0_opsz24"
            android:contentDescription="Delete Page" />
    </LinearLayout>

    <!-- Canvas area -->
    <!-- Update the canvas_container in activity_note_detail.xml -->
    <!-- Canvas area -->
    <FrameLayout
        android:id="@+id/canvas_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#E0E0E0"
        app:layout_constraintTop_toBottomOf="@id/layout_page_navigation">

        <ImageView
            android:id="@+id/image_note"
            android:layout_width="match_parent"
            android:layout_height="match_parent"

            android:scaleType="fitCenter"
            android:visibility="gone" />

        <com.example.app_music.presentation.feature.noteScene.views.DrawingView
            android:id="@+id/drawing_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />
    </FrameLayout>



    <!-- FAB for adding pages -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_page"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@drawable/plus_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@android:color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:backgroundTint="@color/selectedicon" />

    <!-- Small progress bar for save operations -->
    <ProgressBar
        android:id="@+id/save_progress_bar"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Main progress bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>