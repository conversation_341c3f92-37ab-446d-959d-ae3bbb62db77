<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"

    tools:context=".presentation.feature.menu.editusernamescreen.EditUserNameActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/custom_toolbar_container"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/iv_back_arrow"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:src="@drawable/ic_arrow_back_left"
            app:tint="@android:color/black"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/username"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:text="@string/save"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <EditText
        android:id="@+id/et_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_profile_field_border"
        android:padding="12dp"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:inputType="text"
        android:maxLength="20"
        android:text="@string/nameOfUser"
        android:textColor="@android:color/black"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@id/custom_toolbar_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_username_helper"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/description_edit_user_name"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="8dp"
        android:layout_marginStart="18dp"
        android:layout_marginEnd="18dp"
        app:layout_constraintTop_toBottomOf="@id/et_username"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>