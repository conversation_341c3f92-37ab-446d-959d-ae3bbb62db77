<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.menu.premiumuser.PremiumUser">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:src="@drawable/ic_arrow_back_left"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/btn_share"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:text="@string/share"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/top_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/payment_container">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Premium Badge -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/premium_badge_bg"
                android:orientation="horizontal"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_diamond"
                    android:layout_marginEnd="4dp"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/premium"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/snap_solve_premium"
                android:textColor="@android:color/black"
                android:textSize="28sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/unlimited_questions_and_answers"
                android:textColor="@android:color/darker_gray"
                android:textSize="16sp"
                android:gravity="center"
                android:layout_marginBottom="32dp" />

            <!-- Premium Icon -->
            <androidx.cardview.widget.CardView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:layout_marginBottom="40dp"
                app:cardCornerRadius="60dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#FFF0E8">

                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="center"
                    android:src="@drawable/premium_mail_icon" />

            </androidx.cardview.widget.CardView>

            <!-- Features List -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Unlimited questions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_unlimited_questions"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/unlimited_questions_and_answers"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ai_question_resolve"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"

                            android:maxLines="2"
                            android:ellipsize="end"
                            android:lines="2"
                            android:breakStrategy="simple" />


                    </LinearLayout>

                </LinearLayout>

                <!-- Improved answer -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_improved_answer"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/improved_answer"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/smarter_ai"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Remove ads -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_ad_free"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/remove_all_ads"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/remove_add_description"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Note taking -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_note_taking"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/unlimitednote"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/unlimited_note_description"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Detailed explanation -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_detailed_explanation"
                        android:layout_marginEnd="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/detail_explanation"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/detail_explanation_description"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp"
                            android:lines="2"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>


    <LinearLayout
        android:id="@+id/payment_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="@android:color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">


        <Button
            android:id="@+id/btn_zalopay"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/checkout_zalopay"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/selectedicon"
            app:cornerRadius="8dp"
            android:layout_marginBottom="16dp" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/price_for_premium_user"
            android:textColor="@android:color/darker_gray"
            android:textSize="14sp"
            android:gravity="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>