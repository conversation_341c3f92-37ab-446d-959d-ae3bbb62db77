<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".presentation.feature.camera.ResultActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@color/white"
                android:padding="8dp"
                android:src="@drawable/back_img" />

            <TextView
                android:id="@+id/btnHome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:background="@drawable/spinner_border_round_2"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                android:text="Home"
                android:textColor="@color/selectedicon"
                android:textSize="14sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Content ScrollView -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:background="#EDD6C8"
        android:padding="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="0dp">

            <!-- Cropped Image Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:layout_marginVertical="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/imageCropped"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:maxHeight="250dp"
                        android:scaleType="fitCenter"
                        android:layout_margin="4dp"
                        tools:src="@drawable/ic_person" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sau phần ImageView và trước ProgressBar -->
            <TextView
                android:id="@+id/tvSearchQuery"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="8dp"
                android:layout_marginTop="8dp"
                android:background="@color/white"
                android:padding="12dp"
                android:text="Your question"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:cardElevation="2dp" />
            <!-- Loading Progress -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:indeterminateTint="@color/selectedicon"
                android:visibility="visible" />
            <!-- Results Content -->
            <LinearLayout
                android:id="@+id/linearResults"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_marginTop="16dp"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="16dp"
                android:visibility="gone"
                tools:visibility="visible">
                <!-- Results Pagination - Using TextView circles as indicators -->
                <LinearLayout
                    android:id="@+id/paginationContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="Results:"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/pageIndicator1"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/circle_selected"
                        android:gravity="center"
                        android:text="1"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/pageIndicator2"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/circle_unselected"
                        android:gravity="center"
                        android:text="2"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/pageIndicator3"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/circle_unselected"
                        android:gravity="center"
                        android:text="3"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/pageIndicator4"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/circle_unselected"
                        android:gravity="center"
                        android:text="4"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/pageIndicator5"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/circle_unselected"
                        android:gravity="center"
                        android:text="5"
                        android:textAlignment="center"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </LinearLayout>


                <WebView
                    android:id="@+id/webViewResult"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@color/white"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvNotFoundNote"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="You did not find the desired answer?"
                    android:textColor="@color/selectedicon"
                    android:textSize="16sp" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Post to community will be answered quickly"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/btnPostCommunity"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    app:cornerRadius="10dp"
                    android:text="Post to community"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />

                <!-- New "Ask AI" button -->
                <Button
                    android:id="@+id/btnAskAI"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="8dp"
                    app:cornerRadius="10dp"
                    android:backgroundTint="@color/gold"
                    android:text="Ask AI for Solution"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />

                <!-- AI Response Section -->
                <LinearLayout
                    android:id="@+id/aiResponseSection"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:layout_marginTop="16dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="AI Solution"
                        android:textStyle="bold"
                        android:textSize="18sp"
                        android:textColor="@color/black"
                        android:layout_marginBottom="8dp"/>

                    <ProgressBar
                        android:id="@+id/aiProgressBar"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center"
                        android:indeterminateTint="@color/gold"
                        android:visibility="gone" />

                    <WebView
                        android:id="@+id/webViewAIResult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:minHeight="150dp"/>

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</RelativeLayout>
