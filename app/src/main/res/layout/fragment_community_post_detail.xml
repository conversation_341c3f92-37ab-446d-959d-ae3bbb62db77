<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@android:color/white">

    <!-- Top Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/white"
        android:elevation="1dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_back"
                android:contentDescription="Back"
                android:padding="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/post_detail_header"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnEdit"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_edit"
                android:contentDescription="Edit Post"
                android:padding="12dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Topic Tags - Đặt ngay dưới toolbar -->
    <HorizontalScrollView
        android:id="@+id/topicTagsScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        android:paddingVertical="8dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:id="@+id/topicTagsContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:gravity="center_vertical">
            <!-- Tags will be added here dynamically -->
        </LinearLayout>
    </HorizontalScrollView>

    <!-- Content Area with Scroll -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/topicTagsScrollView"
        app:layout_constraintBottom_toTopOf="@id/commentInputArea">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Post Author -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="16dp">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/ivUserAvatar"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/avatar"
                    android:background="@drawable/circle_background"
                    android:scaleType="centerCrop" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="12dp">

                    <TextView
                        android:id="@+id/tvUserName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Lionel Messi"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTimeAgo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="99 hours ago"
                        android:textColor="#777777"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Post Title -->
            <TextView
                android:id="@+id/tvPostTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Who is the GOAT !!"
                android:textColor="@android:color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp" />

            <!-- Post Content -->
            <TextView
                android:id="@+id/tvPostContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley..."
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="16dp" />

            <!-- Single Post Image (if any) -->
            <ImageView
                android:id="@+id/ivPostImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:layout_marginTop="16dp"
                android:src="@drawable/lorem"
                android:visibility="visible" />

            <!-- Post Images Gallery -->
            <TextView
                android:id="@+id/tvImagesLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/post_detail_images"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPostImages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:clipToPadding="false"
                android:paddingHorizontal="12dp"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"
                tools:listitem="@layout/item_post_image"
                tools:visibility="visible" />

            <!-- Likes and Comments Count -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp">

                <TextView
                    android:id="@+id/tvLikesCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="99 likes"
                    android:textColor="#777777"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" • "
                    android:textColor="#777777"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvCommentsCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="99 comments"
                    android:textColor="#777777"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Like and Comment Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="16dp"
                android:paddingVertical="8dp">

                <LinearLayout
                    android:id="@+id/btnLike"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:padding="8dp"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:id="@+id/ivLike"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_liked" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="@string/post_detail_like"
                        android:textColor="#333333"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnComment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:padding="8dp"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_comment" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="@string/post_detail_comment"
                        android:textColor="#333333"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnShare"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:padding="8dp"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_share" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="@string/post_detail_share"
                        android:textColor="#333333"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                android:layout_marginTop="8dp" />

            <!-- Comments Section Title -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/post_detail_comments"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp" />

            <!-- Comments List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewComments"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/comment_item"
                tools:itemCount="2" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Comment Input Area (Fixed at Bottom) -->
    <LinearLayout
        android:id="@+id/commentInputArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:elevation="4dp"
        android:orientation="vertical"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Selected Comment Images Count -->
        <TextView
            android:id="@+id/tvCommentImageCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0/10"
            android:textSize="12sp"
            android:textColor="#AAAAAA"
            android:layout_gravity="end"
            android:layout_marginBottom="4dp"
            android:visibility="gone" />

        <!-- Selected Comment Images -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvSelectedCommentImages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:visibility="gone"
            tools:listitem="@layout/item_selected_image"
            tools:visibility="visible" />

        <!-- Input Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageButton
                android:id="@+id/btnAddImage"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_community_gallery"
                android:padding="8dp" />

            <EditText
                android:id="@+id/etAddComment"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_weight="1"
                android:background="@drawable/bg_profile_field_border"
                android:hint="@string/post_detail_comment_hint"
                android:inputType="textMultiLine"
                android:maxLines="3"
                android:paddingHorizontal="12dp" />

            <ImageButton
                android:id="@+id/btnSendComment"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_send"
                android:padding="8dp" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>