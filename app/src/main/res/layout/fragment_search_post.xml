<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:background="@android:color/white"
    tools:context=".presentation.feature.community.communitySearchPost.SearchPostFragment">

    <!-- Header với back button và search bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_back"
            android:contentDescription="Back" />

        <EditText
            android:id="@+id/etSearch"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/search_background"
            android:hint="@string/post_search_hint"
            android:textColorHint="#AAAAAA"
            android:textSize="14sp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:singleLine="true"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:focusable="true"
            android:focusableInTouchMode="true" />
    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#F0F0F0" />

    <!-- Recent Search Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/post_search_recent"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvDeleteAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/post_search_delete_all"
                android:textColor="@color/selectedicon"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>

        <!-- Recent Search Items - FlexboxLayout sẽ tốt hơn nhưng để đơn giản ta dùng LinearLayout -->
        <LinearLayout
            android:id="@+id/recentSearchContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />
    </LinearLayout>
</LinearLayout>