<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="170dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="50dp"
    android:paddingBottom="60dp">

    <ImageButton
        android:id="@+id/folderButton"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:contentDescription="@string/folder"
        android:src="@drawable/ic_folder"
        android:scaleType="fitXY"
        android:background="@android:color/transparent"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:weightSum="5"
        android:gravity="center"
        android:layout_gravity="center"
        android:layout_marginTop="0dp"
        >
        <TextView
            android:id="@+id/textTitle"
            android:layout_width="0dp"
            android:layout_weight="3.5"
            android:layout_height="wrap_content"
            android:textColor="#2196F3"
            android:textSize="16sp"
            tools:text="Lớp 9"
            android:textStyle="bold"
            android:gravity="center"
            />

        <Button
            android:id="@+id/button_expand_folder"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.5"
            android:background="@color/trans"
            app:icon="@drawable/ic_expand"
            app:iconTint="@color/blue"
            app:iconGravity="textStart"
            android:padding="0dp"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"/>
    </LinearLayout>

    <TextView
        android:id="@+id/textDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textSize="15sp"
        android:textColor="#757575"
        tools:text="31 thg 3, 2025"
        android:gravity="center_horizontal"/>
</LinearLayout>