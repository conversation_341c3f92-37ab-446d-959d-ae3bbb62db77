<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/background"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent">


    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/bottomNavigationView"
        app:layout_constraintEnd_toEndOf="parent"
        app:defaultNavHost="true"
        android:paddingHorizontal="6dp"
        app:navGraph="@navigation/nav_graph" />


    <com.google.android.material.bottomnavigation.BottomNavigationView
        app:layout_constraintTop_toBottomOf="@id/nav_host_fragment"
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/black"

        android:background="@color/background"


        app:labelVisibilityMode="unlabeled"
        app:menu="@menu/bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>
