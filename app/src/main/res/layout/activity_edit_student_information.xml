<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.menu.editstudentInformation.EditStudentInformation">

    <ImageView
        android:id="@+id/iv_back_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_back_left"
        app:tint="@android:color/black"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:padding="16dp"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/title_edit_information_student"
        android:textSize="@dimen/text_large"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/iv_back_arrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/content_edit_student_information"
        android:textSize="@dimen/text_medium"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="4dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="@id/tv_title" />

    <ScrollView
        android:id="@+id/sv_grades_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/btnComplete">

        <RadioGroup
            android:id="@+id/rg_grades"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <RadioButton
                android:id="@+id/rb_grade_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_1"
                android:textSize="@dimen/text_medium"

                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_2"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_3"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_4"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_5"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_6"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_6"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_7"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_7"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_8"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_8"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_9"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_9"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_10"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_11"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_11"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_12"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_12"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_grade_13"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/grade_13"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

            <RadioButton
                android:id="@+id/rb_not_attending"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/out_of_school"
                android:textSize="@dimen/text_medium"
                android:paddingVertical="20dp"
                android:layoutDirection="rtl"
                android:textAlignment="textStart" />

        </RadioGroup>

    </ScrollView>

    <Button
        android:id="@+id/btnComplete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/btnComplete"
        android:backgroundTint="@color/selectedicon"
        android:textColor="@android:color/white"
        android:insetTop="0dp"
        android:insetBottom="0dp"
        app:cornerRadius="8dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>