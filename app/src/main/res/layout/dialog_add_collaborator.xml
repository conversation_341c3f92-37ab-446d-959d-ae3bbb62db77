<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Enter Email Address"
        android:textColor="@android:color/black"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_email"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="<EMAIL>"
        android:inputType="textEmailAddress"
        android:layout_marginBottom="16dp"
        android:padding="12dp"
        android:background="@drawable/bg_profile_field_border" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="This person will be able to view and edit this note"
        android:textColor="@android:color/darker_gray"
        android:textSize="14sp" />

</LinearLayout>