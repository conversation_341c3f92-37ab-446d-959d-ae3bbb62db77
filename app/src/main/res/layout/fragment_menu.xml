<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".presentation.feature.menu.MenuFragment"
    android:background="@android:color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutTopBar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="8dp">

        <TextView
            android:id="@+id/textViewMenuTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@string/nameOfScreenMenu"
            android:textSize="@dimen/text_large"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/btnSetting"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_setting"
            android:clickable="true"
            android:focusable="true"
            android:padding="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/textViewMenuTitle"
            app:layout_constraintBottom_toBottomOf="@id/textViewMenuTitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/layoutTopBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="16dp">

            <!-- Profile Section -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewProfileIcon"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="@color/colorForDevider"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/imageViewProfileIcon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_person" />
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/textViewUserName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:text="@string/nameOfUser"
                android:textColor="@android:color/black"
                android:textSize="@dimen/text_medium"
                app:layout_constraintStart_toEndOf="@id/cardViewProfileIcon"
                app:layout_constraintTop_toTopOf="@id/cardViewProfileIcon"
                app:layout_constraintBottom_toBottomOf="@id/cardViewProfileIcon"
                app:layout_constraintEnd_toStartOf="@id/btnEditProfile"/>

            <Button
                android:id="@+id/btnEditProfile"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_marginEnd="16dp"
                android:text="@string/btnEdit"
                android:textAllCaps="false"
                android:backgroundTint="@color/selectedicon"
                android:textColor="@android:color/white"
                android:minWidth="60dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                app:cornerRadius="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/textViewUserName"
                app:layout_constraintBottom_toBottomOf="@id/textViewUserName"/>

            <View
                android:id="@+id/divider1"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="20dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/colorForDevider"
                app:layout_constraintTop_toBottomOf="@id/btnEditProfile"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <!-- Recommend/Premium Section -->
            <TextView
                android:id="@+id/textViewRecommendTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/recommend_product"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider1"/>

            <FrameLayout
                android:id="@+id/frameLayoutPremium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/textViewRecommendTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <!-- Premium Layout (cho user thường) -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layoutPremium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/imageViewPremiumIcon"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_diamond"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" />

                    <TextView
                        android:id="@+id/textViewPremiumLabel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:text="@string/premium"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/text_medium"
                        app:layout_constraintStart_toEndOf="@id/imageViewPremiumIcon"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <TextView
                        android:id="@+id/textViewStartFree"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="@string/start_for_free"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toStartOf="@id/imageViewPremiumArrow"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <ImageView
                        android:id="@+id/imageViewPremiumArrow"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@android:color/darker_gray"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- Transaction History Layout (cho user premium) -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layoutTransactionHistory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/textViewTransactionLabel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/history_receipt"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/text_medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <TextView
                        android:id="@+id/textViewTransactionStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="@string/premium_active"
                        android:textColor="@color/selectedicon"
                        android:textSize="12sp"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        android:paddingTop="2dp"
                        android:paddingBottom="2dp"
                        app:layout_constraintEnd_toStartOf="@id/imageViewTransactionArrow"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <ImageView
                        android:id="@+id/imageViewTransactionArrow"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@android:color/darker_gray"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </FrameLayout>

            <!-- Divider 2 -->
            <View
                android:id="@+id/divider2"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/colorForDevider"
                app:layout_constraintTop_toBottomOf="@id/frameLayoutPremium"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <!-- Question History Section -->
            <TextView
                android:id="@+id/textViewHistoryTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/question_history"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider2"/>

            <TextView
                android:id="@+id/textViewRecentSearches"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="@string/recent_searches"
                android:textSize="@dimen/text_medium"
                android:textColor="@color/black"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewHistoryTitle"
                app:layout_constraintEnd_toEndOf="parent"/>



            <!-- Divider 3 -->
            <View
                android:id="@+id/divider3"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/colorForDevider"
                app:layout_constraintTop_toBottomOf="@id/textViewRecentSearches"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <!-- Question Section -->
            <TextView
                android:id="@+id/textViewQuestionTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/question"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider3"/>

            <TextView
                android:id="@+id/textViewSearch"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="@string/search"
                android:textSize="@dimen/text_medium"
                android:textColor="@color/black"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewQuestionTitle"
                app:layout_constraintEnd_toEndOf="parent"/>



            <!-- Divider 4 -->
            <View
                android:id="@+id/divider4"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/colorForDevider"
                app:layout_constraintTop_toBottomOf="@id/textViewSearch"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <!-- Tools Section -->
            <TextView
                android:id="@+id/textViewToolsTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/tools"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider4"/>

            <TextView
                android:id="@+id/textViewNote"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:text="@string/note"
                android:textColor="@color/black"
                android:textSize="@dimen/text_medium"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewToolsTitle"
                app:layout_constraintEnd_toEndOf="parent"/>

            <!-- Divider 5 -->
            <View
                android:id="@+id/divider5"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:background="@color/colorForDevider"
                app:layout_constraintTop_toBottomOf="@id/textViewNote"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>