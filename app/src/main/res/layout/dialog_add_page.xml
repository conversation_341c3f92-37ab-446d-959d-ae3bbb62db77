<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Add New Page"
        android:textSize="18sp"
        android:textColor="@android:color/black"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Choose how to add a new page:"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="8dp">

        <LinearLayout
            android:id="@+id/layout_blank_page"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_note"
                android:tint="@color/selectedicon"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Blank Page"
                android:textColor="@android:color/black"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_camera_page"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/camera"
                android:tint="@color/selectedicon"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Take Photo"
                android:textColor="@android:color/black"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_gallery_page"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/upload"
                android:tint="@color/selectedicon"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="From Gallery"
                android:textColor="@android:color/black"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <Button
        android:id="@+id/button_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Cancel"
        android:layout_marginTop="16dp"
        android:backgroundTint="@android:color/darker_gray"
        android:textColor="@android:color/white" />
</LinearLayout>