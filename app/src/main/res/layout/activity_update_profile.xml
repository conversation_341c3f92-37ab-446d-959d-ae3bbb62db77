<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.auth.UpdateProfileActivity">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/complete_profile"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="40dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/complete_profile_description"
        android:textSize="16sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="@id/tv_title" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        app:layout_constraintBottom_toTopOf="@id/btn_continue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/first_name"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_first_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="textPersonName"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/last_name"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <EditText
                android:id="@+id/et_last_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="textPersonName"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/date_of_birth"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <EditText
                android:id="@+id/et_dob"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="date"
                android:focusable="false"
                android:clickable="true"
                android:hint="@string/date_format"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/student_information"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <Spinner
                android:id="@+id/spinner_grade"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/spinner_border_round"
                android:padding="12dp"
                android:layout_marginTop="8dp" />

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/btn_continue"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/continue_text"
        android:backgroundTint="@color/selectedicon"
        android:textColor="@android:color/white"
        android:layout_marginBottom="24dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>