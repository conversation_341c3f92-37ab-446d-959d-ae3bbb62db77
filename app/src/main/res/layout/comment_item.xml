<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/ivCommentUserAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/avatar"
        android:background="@drawable/circle_background"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toEndOf="@id/ivCommentUserAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCommentUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lionel Messi"
                android:textColor="@android:color/black"
                android:textStyle="bold"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvCommentTimeAgo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="99 hours ago"
                android:textColor="#777777"
                android:textSize="12sp"
                android:layout_marginStart="8dp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvCommentContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Lorem ipsum is simply dummy text of the printing and typesetting industry"
            android:textColor="#333333"
            android:textSize="14sp"
            android:layout_marginTop="4dp" />

        <!-- RecyclerView cho ảnh comment -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCommentImages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:listitem="@layout/item_comment_image" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/tvCommentReply"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Trả lời"
                android:textColor="@color/selectedicon"
                android:textSize="12sp"
                android:paddingVertical="4dp"
                android:paddingHorizontal="8dp"
                android:background="?attr/selectableItemBackground" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>