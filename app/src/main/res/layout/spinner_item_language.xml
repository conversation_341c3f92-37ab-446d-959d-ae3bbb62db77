<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingStart="12dp"
    android:paddingEnd="12dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <ImageView
        android:id="@+id/iv_language_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_language"
        android:layout_marginEnd="8dp"
        android:contentDescription="@string/language" />

    <TextView
        android:id="@+id/tv_language_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:textColor="@android:color/black"
        android:textSize="16sp" />

</LinearLayout>