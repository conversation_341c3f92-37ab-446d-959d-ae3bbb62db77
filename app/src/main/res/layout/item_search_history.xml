<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:id="@+id/imageViewHistory"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_search_history" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewQuestion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/selectedicon"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="What is the formula for calculating the area of a circle?" />

            <TextView
                android:id="@+id/textViewDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/gray"
                android:textSize="12sp"
                tools:text="21/05/2025" />

<!--            <TextView-->
<!--                android:id="@+id/textViewType"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginTop="2dp"-->
<!--                android:background="@drawable/spinner_border_round_2"-->
<!--                android:paddingHorizontal="8dp"-->
<!--                android:paddingVertical="2dp"-->
<!--                android:text="Text Search"-->
<!--                android:textColor="@color/selectedicon"-->
<!--                android:textSize="10sp" />-->

        </LinearLayout>

<!--        <ImageView-->
<!--            android:layout_width="24dp"-->
<!--            android:layout_height="24dp"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:rotation="180"-->
<!--            android:src="@drawable/ic_arrow_back_left"-->
<!--            app:tint="@color/black" />-->

    </LinearLayout>

</androidx.cardview.widget.CardView>