<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".presentation.feature.detail_notification.NotificationDetailActivity">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="12dp"
            android:src="@drawable/ic_arrow_back_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black" />

        <TextView
            android:id="@+id/tvToolbarTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/detail_notification"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">


            <ImageView
                android:id="@+id/ivNotificationIcon"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/notification_icon_background"
                android:padding="16dp"
                android:src="@drawable/ic_notification"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />


            <TextView
                android:id="@+id/tvNotificationTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivNotificationIcon"
                app:layout_constraintTop_toTopOf="@id/ivNotificationIcon"
                tools:text="@string/success_payment" />


            <TextView
                android:id="@+id/tvNotificationTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="16dp"
                android:textColor="#9E9E9E"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivNotificationIcon"
                app:layout_constraintTop_toBottomOf="@id/tvNotificationTitle"
                tools:text="23 tháng 5, 2025 · 16:01" />


            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="24dp"
                android:background="#E0E0E0"
                app:layout_constraintTop_toBottomOf="@id/ivNotificationIcon" />


            <TextView
                android:id="@+id/tvNotificationContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:lineSpacingMultiplier="1.2"
                android:textColor="#333333"
                android:textSize="16sp"
                app:layout_constraintTop_toBottomOf="@id/divider"
                tools:text="Chúc mừng bạn đã trở thành Premium User! Gói đăng ký: MONTHLY. Thời hạn: 1 tháng. Hết hạn vào: 2025-06-23. Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!" />


            <androidx.cardview.widget.CardView
                android:id="@+id/cardAdditionalInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:cardBackgroundColor="#F5F5F5"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:layout_constraintTop_toBottomOf="@id/tvNotificationContent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/detail"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="ID"
                            android:textColor="#757575"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvNotificationId"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            tools:text="9" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/type_notification"
                            android:textColor="#757575"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvNotificationType"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            tools:text="notification" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/day_create"
                            android:textColor="#757575"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvNotificationDate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            tools:text="2025-05-23" />
                    </LinearLayout>


                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <LinearLayout
                android:id="@+id/actionsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@id/cardAdditionalInfo">




                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSecondaryAction"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:paddingVertical="12dp"
                    android:text="@string/deletye_notification"
                    android:textColor="@color/red"
                    android:textSize="16sp"
                    app:cornerRadius="8dp"
                    app:strokeColor="@color/red" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>