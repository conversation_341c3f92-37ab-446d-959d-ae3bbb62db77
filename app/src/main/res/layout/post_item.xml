<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/colorForPost"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp">

        <!-- User Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/ivUserAvatar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/avatar"
                android:background="@drawable/circle_background"
                android:scaleType="centerCrop" />

            <TextView
                android:id="@+id/tvUserName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Lionel Messi"
                android:textColor="#333333"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvTimeAgo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="99 hours ago"
                android:textColor="#777777"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- Post Title -->
        <TextView
            android:id="@+id/tvPostTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Who is the GOAT !!"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- Post Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tvPostContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.7"
                android:text="Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley..."
                android:textColor="#333333"
                android:textSize="14sp"
                android:maxLines="3"
                android:ellipsize="end" />

            <ImageView
                android:id="@+id/ivPostImage"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.3"
                android:layout_marginStart="8dp"
                android:scaleType="centerCrop"
                android:src="@drawable/lorem"
                android:contentDescription="Post image" />
        </LinearLayout>

        <!-- Tags và Actions cùng hàng -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Tags bên trái -->
            <HorizontalScrollView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvTag1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Popular"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp"
                        android:paddingTop="4dp"
                        android:paddingBottom="4dp"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/tag_backround" />

                    <TextView
                        android:id="@+id/tvTag2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Popular"
                        android:textColor="#FFFFFF"
                        android:textSize="12sp"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp"
                        android:paddingTop="4dp"
                        android:paddingBottom="4dp"
                        android:background="@drawable/tag_backround" />
                </LinearLayout>
            </HorizontalScrollView>

            <!-- Actions bên phải -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="16dp">

                <LinearLayout
                    android:id="@+id/btnLike"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/selectableItemBackground"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <ImageView
                        android:id="@+id/ivLike"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_liked"/>

                    <TextView
                        android:id="@+id/tvLikeCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="99"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnComment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp">

                    <ImageView
                        android:id="@+id/ivComment"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_comment" />

                    <TextView
                        android:id="@+id/tvCommentCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="2"
                        android:textColor="#333333"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        android:layout_gravity="bottom" />
</androidx.cardview.widget.CardView>