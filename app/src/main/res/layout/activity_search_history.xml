<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".presentation.feature.searchhistory.SearchHistoryActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@color/white"
                android:padding="8dp"
                android:src="@drawable/back_img" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Search History"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Search Bar -->
    <LinearLayout
        android:id="@+id/searchContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <EditText
            android:id="@+id/editTextSearch"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/border_round_12"
            android:drawableStart="@drawable/ic_home_search"
            android:drawablePadding="8dp"
            android:drawableTint="#9E633E"
            android:hint="Search in history"
            android:paddingHorizontal="15dp"
            android:textColorHint="@color/selectedicon"
            android:textSize="15sp" />

    </LinearLayout>

    <!-- RecyclerView for Search History -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchContainer">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewHistory"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingHorizontal="16dp"
            android:paddingBottom="16dp"
            tools:listitem="@layout/item_search_history" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Loading Progress -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:indeterminateTint="@color/selectedicon"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/swipeRefreshLayout" />

    <!-- Empty State View -->
    <LinearLayout
        android:id="@+id/emptyStateView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/swipeRefreshLayout">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:alpha="0.3"
            android:src="@drawable/ic_search_history"
            app:tint="@color/selectedicon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="No search history found"
            android:textColor="@color/gray"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
