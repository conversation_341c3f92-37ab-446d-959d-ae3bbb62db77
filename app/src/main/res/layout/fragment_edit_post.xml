<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.community.communityEditPost.EditPostFragment">

    <!-- Top Bar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btnClose"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Close"
            android:padding="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/post_edit_header"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/btnUpdate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/post_edit_edit"
            android:textSize="16sp"
            android:textColor="@color/selectedicon"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0"
        app:layout_constraintTop_toBottomOf="@id/topBar" />

    <!-- Content Area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/divider"
        app:layout_constraintBottom_toTopOf="@id/bottomBar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Topic Selection -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/topicSelector"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="12dp"
                android:paddingHorizontal="8dp"
                android:layout_marginVertical="8dp"
                android:background="@drawable/topic_selector_background"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/tvTopicLabel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/posting_choose_topic"
                    android:textSize="16sp"
                    android:textColor="@android:color/black"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivArrow" />

                <ImageView
                    android:id="@+id/ivArrow"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0" />

            <!-- Title Input -->
            <EditText
                android:id="@+id/etTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/posting_hint_title"
                android:textSize="16sp"
                android:textColorHint="#AAAAAA"
                android:paddingVertical="12dp"
                android:maxLines="2"
                android:inputType="textCapSentences|textMultiLine"
                android:imeOptions="actionNext" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0" />

            <!-- Content Input -->
            <EditText
                android:id="@+id/etContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/posting_hint_content"
                android:textSize="16sp"
                android:textColorHint="#AAAAAA"
                android:paddingVertical="12dp"
                android:gravity="top"
                android:inputType="textCapSentences|textMultiLine"
                android:minLines="5" />

            <!-- Selected Topics Container -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/posting_topic_selected"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone"
                android:id="@+id/tvSelectedTopicsTitle" />

            <com.google.android.flexbox.FlexboxLayout
                android:id="@+id/selectedTopicsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:flexWrap="wrap"
                app:alignItems="flex_start"
                android:layout_marginBottom="16dp"
                android:visibility="gone" />

            <!-- Selected Images Preview -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/posting_image_selected"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone"
                android:id="@+id/tvSelectedImagesTitle" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvSelectedImages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="100dp"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3"
                tools:listitem="@layout/item_selected_image"
                tools:visibility="visible" />

        </LinearLayout>
    </ScrollView>

    <!-- Bottom Bar -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottomBar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvImageCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0/10"
            android:textSize="14sp"
            android:textColor="#AAAAAA"
            android:layout_marginEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toStartOf="parent">

            <ImageButton
                android:id="@+id/btnAddImage"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_community_gallery"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/posting_add_image"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/posting_add_image"
                android:textSize="14sp"
                android:textColor="#333333" />

        </LinearLayout>

        <!-- Progress bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/tvImageCount"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>