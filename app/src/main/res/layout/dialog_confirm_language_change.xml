<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvConfirmTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/change_language_title"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tvConfirmMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/change_language_message"
        android:textSize="14sp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/tvConfirmTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <Button
        android:id="@+id/btnConfirm"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/confirm"
        android:backgroundTint="@color/selectedicon"
        android:textColor="@android:color/white"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toBottomOf="@id/tvConfirmMessage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnCancel"/>

    <Button
        android:id="@+id/btnCancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/cancel"
        android:backgroundTint="@android:color/darker_gray"
        android:textColor="@android:color/white"
        android:layout_marginStart="8dp"
        app:layout_constraintTop_toTopOf="@id/btnConfirm"
        app:layout_constraintStart_toEndOf="@id/btnConfirm"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>