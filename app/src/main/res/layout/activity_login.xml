<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.auth.LoginActivity">

    <!-- Form container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/form_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Spinner language - đ<PERSON> đ<PERSON> di chuyển vào trong form container -->
        <Spinner
            android:id="@+id/spinner_language"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="140dp"
            android:background="@drawable/spinner_bg"
            android:padding="8dp"
            android:layout_margin="16dp"
            android:spinnerMode="dropdown"
            android:popupBackground="@android:color/white"
            android:dropDownWidth="200dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:text="@string/login"
            android:textColor="@android:color/black"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/spinner_language" /> <!-- Thay đổi constraint từ top_toTopOf="parent" thành top_toBottomOf="@id/spinner_language" -->

        <TextView
            android:id="@+id/tv_registration_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@color/selectedicon"
            android:textSize="16sp"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5" />

        <EditText
            android:id="@+id/et_username"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/bg_profile_field_border"
            android:hint="@string/username_or_email"
            android:inputType="text"
            android:padding="12dp"
            android:textColor="@android:color/black"
            android:textColorHint="@android:color/darker_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_registration_message" />

        <EditText
            android:id="@+id/et_password"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/bg_profile_field_border"
            android:hint="@string/password"
            android:inputType="textPassword"
            android:padding="12dp"
            android:textColor="@android:color/black"
            android:textColorHint="@android:color/darker_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_username" />

        <CheckBox
            android:id="@+id/cb_remember_me"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="8dp"
            android:text="@string/remember_me"
            android:textColor="@android:color/black"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_password" />

        <TextView
            android:id="@+id/tv_forgot_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:text="@string/forgot_password"
            android:textColor="@color/selectedicon"
            app:layout_constraintBottom_toBottomOf="@id/cb_remember_me"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/cb_remember_me" />

        <Button
            android:id="@+id/btn_login"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:backgroundTint="@color/selectedicon"
            android:text="@string/login"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cb_remember_me" />

        <TextView
            android:id="@+id/tv_or"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/or"
            android:textColor="@android:color/darker_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_login" />

        <View
            android:id="@+id/divider_left"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="16dp"
            android:background="@color/colorForDevider"
            app:layout_constraintBottom_toBottomOf="@id/tv_or"
            app:layout_constraintEnd_toStartOf="@id/tv_or"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_or" />

        <View
            android:id="@+id/divider_right"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="24dp"
            android:background="@color/colorForDevider"
            app:layout_constraintBottom_toBottomOf="@id/tv_or"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_or"
            app:layout_constraintTop_toTopOf="@id/tv_or" />

        <TextView
            android:id="@+id/tv_no_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/no_account"
            android:textColor="@android:color/black"
            app:layout_constraintEnd_toStartOf="@id/tv_register"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider_left" />

        <TextView
            android:id="@+id/tv_register"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="@string/register_now"
            android:textColor="@color/selectedicon"
            app:layout_constraintTop_toTopOf="@id/tv_no_account"
            app:layout_constraintBottom_toBottomOf="@id/tv_no_account"
            app:layout_constraintStart_toEndOf="@id/tv_no_account"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- ProgressBar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>