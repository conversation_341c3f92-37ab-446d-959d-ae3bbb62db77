<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        app:elevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvCommunity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="@string/community_header"
                android:textColor="@android:color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnSearch"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_search"
                app:layout_constraintBottom_toBottomOf="@+id/tvCommunity"
                app:layout_constraintEnd_toStartOf="@+id/btnProfile"
                app:layout_constraintTop_toTopOf="@+id/tvCommunity" />

            <ImageButton
                android:id="@+id/btnProfile"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_profile"
                app:layout_constraintBottom_toBottomOf="@+id/btnSearch"
                app:layout_constraintEnd_toStartOf="@+id/btnNotification"
                app:layout_constraintTop_toTopOf="@+id/btnSearch" />

            <ImageButton
                android:id="@+id/btnNotification"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_notification"
                app:layout_constraintBottom_toBottomOf="@+id/btnProfile"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/btnProfile" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                app:layout_constraintBottom_toBottomOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <RadioGroup
                        android:id="@+id/radioGroup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <RadioButton
                            android:id="@+id/rbAll"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:text="@string/community_all"
                            android:checked="true"
                            android:background="@drawable/tab_selector"
                            android:button="@null"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:layout_marginEnd="8dp"
                            android:textColor="@color/tab_text_selector"
                            android:gravity="center" />
                    </RadioGroup>
                </HorizontalScrollView>

                <!-- Post Items -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/post_item" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabCreate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:backgroundTint="@color/selectedicon"
        android:contentDescription="Create post"
        android:src="@drawable/ic_pencil"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>