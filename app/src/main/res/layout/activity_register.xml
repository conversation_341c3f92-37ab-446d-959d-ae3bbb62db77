<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.auth.RegisterActivity">

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_back_left"
        android:padding="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/create_account"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="30dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintTop_toBottomOf="@id/btn_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/register_description"
        android:textSize="16sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="@id/tv_title" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        app:layout_constraintBottom_toTopOf="@id/btn_register"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/username"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="text"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/email"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <EditText
                android:id="@+id/et_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="textEmailAddress"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/phone_number"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <EditText
                android:id="@+id/et_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="phone"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/password"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <EditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="textPassword"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/confirm_password"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                android:layout_marginTop="16dp" />

            <EditText
                android:id="@+id/et_confirm_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile_field_border"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:inputType="textPassword"
                android:textColor="@android:color/black"
                android:textColorHint="@android:color/darker_gray" />

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/btn_register"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/register"
        android:backgroundTint="@color/selectedicon"
        android:textColor="@android:color/white"
        android:layout_marginBottom="24dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintBottom_toTopOf="@id/tv_already_account"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_already_account"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/already_have_account"
        android:textColor="@android:color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_login"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:layout_marginBottom="24dp" />

    <TextView
        android:id="@+id/tv_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_here"
        android:textColor="@color/selectedicon"
        android:layout_marginStart="4dp"
        app:layout_constraintTop_toTopOf="@id/tv_already_account"
        app:layout_constraintStart_toEndOf="@id/tv_already_account"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>