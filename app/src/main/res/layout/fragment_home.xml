<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".presentation.feature.home.HomeFragment">

    <!-- Top bar with profile, stars and notification -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingTop="10dp"
        android:paddingBottom="5dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/profile_icon"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:src="@drawable/ic_home_avatar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <ImageView
            android:id="@+id/notification_icon"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@drawable/ic_outline_notifications"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Main content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_bar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- SnapSolve Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Ask SnapSolve anything"
                android:textAlignment="center"
                android:textColor="@color/selectedicon"
                android:textSize="24sp"
                android:textStyle="bold"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/search_edit_text"
                    android:layout_width="0dp"
                    android:layout_height="53dp"
                    android:layout_weight="1"
                    android:background="@drawable/border_round_12"
                    android:drawableStart="@drawable/ic_home_search"
                    android:drawablePadding="8dp"
                    android:drawableTint="#9E633E"
                    android:hint="Type your question"
                    android:paddingHorizontal="15dp"
                    android:textColorHint="@color/selectedicon"
                    android:textSize="15sp" />

                <ImageButton
                    android:id="@+id/camera_button"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_marginStart="14dp"
                    android:background="@drawable/spinner_border_round_orange_2"
                    android:padding="8dp"
                    android:src="@drawable/ic_home_camera"
                    app:tint="@color/white" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="30dp"
                android:background="#9E633E" />

            <!-- Utilities Section -->
            <LinearLayout
                android:id="@+id/utilities_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/border_round_16"
                android:orientation="vertical"
                android:paddingHorizontal="14dp"
                android:paddingBottom="14dp"
                android:paddingTop="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="Utilities"
                    android:textColor="@color/selectedicon"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- First Utility: Dictionary -->
                    <LinearLayout
                        android:id="@+id/dictionaryButton"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:background="@drawable/back_utilities"
                        android:layout_weight="1"
                        android:layout_marginEnd="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:src="@drawable/image_dictionary"
                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Từ điển"
                            android:textColor="@color/selectedicon"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <!-- Second Utility: Calculator -->
                    <LinearLayout
                        android:id="@+id/calculator_item"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:background="@drawable/back_utilities"
                        android:layout_weight="1"
                        android:layout_marginEnd="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:src="@drawable/image_calculator"
                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Máy tính"
                            android:textColor="@color/selectedicon"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <!-- Third Utility: Entertainment 1 -->
                    <LinearLayout
                        android:id="@+id/entertainment_item1"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:background="@drawable/back_utilities"
                        android:layout_weight="1"
                        android:layout_marginEnd="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:src="@drawable/image_ghost"
                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giải trí"
                            android:textColor="@color/selectedicon"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <!-- Fourth Utility: Entertainment 2 -->
                    <LinearLayout
                        android:id="@+id/entertainment_item2"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:background="@drawable/back_utilities"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:src="@drawable/image_ghost"
                            />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giải trí"
                            android:textColor="@color/selectedicon"
                            android:textSize="10sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- Update the search history section (previously missions) -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="20dp"
                android:background="@drawable/border_round_16"
                android:paddingHorizontal="14dp"
                android:paddingBottom="14dp"
                android:paddingTop="12dp">

                <TextView
                    android:id="@+id/missions_section_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:text="@string/recent_searches"
                    android:textColor="@color/selectedicon"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- History Item 1 -->
                <LinearLayout
                    android:id="@+id/mission_items_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp"
                    android:paddingHorizontal="0dp">

                    <ImageView
                        android:id="@+id/search_history_image1"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_search_history"
                        />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginTop="8dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/search_history_question1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Search question example..."
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textColor="@color/selectedicon"
                            android:textStyle="bold"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/search_history_date1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="21/05/2025"
                            android:textColor="@color/gray"
                            android:textSize="10sp" />
                    </LinearLayout>

<!--                    <ImageView-->
<!--                        android:layout_width="24dp"-->
<!--                        android:layout_height="24dp"-->
<!--                        android:layout_gravity="center_vertical"-->
<!--                        android:src="@drawable/ic_arrow_back_left"-->
<!--                        android:rotation="180"-->
<!--                        app:tint="@color/black" />-->
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:id="@+id/divider1"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="8dp"
                    android:background="#9E633E" />

                <!-- History Item 2 -->
                <LinearLayout
                    android:id="@+id/mission_items_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp"
                    android:paddingHorizontal="0dp">

                    <ImageView
                        android:id="@+id/search_history_image2"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_search_history"
                        />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginTop="8dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/search_history_question2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Search question example..."
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textColor="@color/selectedicon"
                            android:textStyle="bold"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/search_history_date2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="20/05/2025"
                            android:textColor="@color/gray"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_arrow_back_left"
                        android:rotation="180"
                        app:tint="@color/black" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:id="@+id/divider2"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="8dp"
                    android:background="#9E633E" />

                <!-- History Item 3 -->
                <LinearLayout
                    android:id="@+id/mission_items_3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp"
                    android:paddingHorizontal="0dp">

                    <ImageView
                        android:id="@+id/search_history_image3"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_search_history"
                        />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginTop="8dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/search_history_question3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Search question example..."
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:textColor="@color/selectedicon"
                            android:textStyle="bold"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/search_history_date3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="19/05/2025"
                            android:textColor="@color/gray"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_arrow_back_left"
                        android:rotation="180"
                        app:tint="@color/black" />
                </LinearLayout>

                <!-- See more button -->
                <Button
                    android:id="@+id/open_missions_button"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="8dp"
                    app:cornerRadius="10dp"
                    android:backgroundTint="#F3B24B"
                    android:text="@string/see_more_searches"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:elevation="0dp"/>
            </LinearLayout>

            <!-- Social Media Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/facebook_button"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="14dp"
                    android:layout_weight="1"
                    app:cornerRadius="10dp"
                    android:backgroundTint="#166EB7"
                    android:drawableStart="@drawable/ic_baseline_facebook"
                    android:drawableTint="@color/white"
                    android:gravity="center"
                    android:paddingStart="10dp"
                    android:paddingEnd="14dp"
                    android:text="Facebook"
                    android:textSize="14sp"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />

                <Button
                    android:id="@+id/tiktok_button"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_weight="1"
                    app:cornerRadius="10dp"                    android:backgroundTint="#2A2A2A"
                    android:drawableStart="@drawable/ic_baseline_tiktok"
                    android:drawableTint="@color/white"
                    android:gravity="center"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:text="Tiktok"
                    android:textSize="14sp"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />
            </LinearLayout>

            <!-- Extra space at bottom -->
            <View
                android:layout_width="match_parent"
                android:layout_height="40dp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>