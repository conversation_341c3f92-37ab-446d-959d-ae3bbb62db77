<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@android:color/white">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="#2196F3">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_brush"
            android:tint="@android:color/white"
            android:layout_marginEnd="12dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Tùy chỉnh độ dày nét vẽ"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"/>
    </LinearLayout>

    <!-- Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Preview -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="#F5F5F5"
            android:layout_marginBottom="16dp"
            android:padding="8dp">

            <View
                android:id="@+id/stroke_preview"
                android:layout_width="200dp"
                android:layout_height="5dp"
                android:background="#000000"
                android:layout_gravity="center"/>
        </FrameLayout>

        <!-- Width value -->
        <TextView
            android:id="@+id/width_value_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Độ dày: 5"
            android:textSize="16sp"
            android:textColor="#2196F3"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp"/>

        <!-- SeekBar -->
        <SeekBar
            android:id="@+id/stroke_width_seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:max="30"
            android:progress="5"
            android:progressTint="#2196F3"
            android:thumbTint="#2196F3"
            android:layout_marginBottom="16dp"/>

        <!-- Label for presets -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Chọn nhanh độ dày"
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginBottom="8dp"/>

        <!-- Preset buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/btn_thin"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="Mảnh"
                android:textColor="#333333"
                android:gravity="center"
                android:background="@drawable/button_rounded_white"
                android:layout_marginEnd="6dp"/>

            <TextView
                android:id="@+id/btn_medium"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="Vừa"
                android:textColor="#333333"
                android:gravity="center"
                android:background="@drawable/button_rounded_white"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="3dp"/>

            <TextView
                android:id="@+id/btn_thick"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="Dày"
                android:textColor="#333333"
                android:gravity="center"
                android:background="@drawable/button_rounded_white"
                android:layout_marginStart="6dp"/>
        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginBottom="16dp"/>

        <!-- Actions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:text="Hủy"
                android:textColor="#757575"
                android:gravity="center"
                android:background="@drawable/button_light_gray"
                android:layout_marginEnd="12dp"/>

            <TextView
                android:id="@+id/btn_apply"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:text="Áp dụng"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:gravity="center"
                android:background="@drawable/button_blue"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>