<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/topic_dialog_background">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/posting_topic_header"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="16dp" />


    <com.google.android.flexbox.FlexboxLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/topicsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        app:flexWrap="wrap"
        app:flexDirection="row"
        app:justifyContent="flex_start"
        app:alignItems="flex_start">
    </com.google.android.flexbox.FlexboxLayout>

    <Button
        android:id="@+id/btnSelectTopic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@color/selectedicon"
        android:text="@string/posting_select"
        android:textColor="@android:color/white" />

</LinearLayout>