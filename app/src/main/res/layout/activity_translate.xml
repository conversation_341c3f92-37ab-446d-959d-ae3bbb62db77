<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        android:elevation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@color/white"
                android:padding="8dp"
                android:src="@drawable/back_img" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Translate"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Input Text Area -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="#F6EBE4">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <EditText
                    android:id="@+id/etInputText"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:gravity="top"
                    android:hint="Type your text..."
                    android:inputType="textMultiLine"
                    android:textColor="@color/black"
                    android:textColorHint="@color/gray"
                    android:textSize="16sp" />

                <!-- Character count -->
                <TextView
                    android:id="@+id/tvCharCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:text="0/1000 characters"
                    android:textColor="@color/gray"
                    android:textSize="12sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Language Selection -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Source Language -->
            <TextView
                android:id="@+id/tvSourceLanguage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/language_selector_bg"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:text="Vietnamese"
                android:textColor="@color/selectedicon"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- Swap Button -->
            <ImageButton
                android:id="@+id/btnSwapLanguages"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginHorizontal="16dp"
                android:background="@drawable/circle_button_bg"
                android:src="@drawable/ic_swap_horizontal"
                android:contentDescription="Swap languages" />

            <!-- Target Language -->
            <TextView
                android:id="@+id/tvTargetLanguage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/language_selector_bg"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:text="English"
                android:textColor="@color/selectedicon"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Translation Result Area -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="#F6EBE4">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Translation..."
                    android:textColor="@color/gray"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <!-- Translation Result -->
                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvTranslationResult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:hint="Translation will appear here..."
                        android:textColor="@color/black"
                        android:textColorHint="@color/gray"
                        android:textSize="16sp"
                        android:textIsSelectable="true" />

                </ScrollView>

                <!-- Translation Actions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="end"
                    android:layout_marginTop="8dp">

                    <!-- Copy Button -->
                    <ImageButton
                        android:id="@+id/btnCopyTranslation"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_button_bg"
                        android:src="@drawable/ic_copy"
                        android:layout_marginEnd="8dp"
                        android:contentDescription="Copy translation"
                        android:visibility="gone" />

                    <!-- Speak Button -->
                    <ImageButton
                        android:id="@+id/btnSpeakTranslation"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_button_bg"
                        android:src="@drawable/ic_volume_up"
                        android:contentDescription="Speak translation"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:indeterminateTint="@color/selectedicon"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>