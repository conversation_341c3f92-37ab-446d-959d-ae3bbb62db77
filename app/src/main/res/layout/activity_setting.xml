<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.menu.setting.SettingActivity">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/custom_toolbar_container"
        android:layout_width="match_parent"

        android:layout_height="?attr/actionBarSize"

        android:background="@android:color/white"
        android:elevation="2dp"

        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">


    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:src="@drawable/ic_arrow_back_left"
        app:tint="@android:color/black"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />


    <TextView
        android:id="@+id/tv_profile_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/setting"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="16dp">

            <!-- Help Center Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/help_center"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp"/>




            <TextView
                android:id="@+id/tvLanguage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/language"
                android:textSize="16sp"
                android:textColor="@android:color/black"
                android:padding="16dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"/>


            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorForDevider"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"/>






            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorForDevider"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"/>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tvAccountLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/account"
                    android:textSize="16sp"
                    android:textColor="@android:color/black"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <TextView
                    android:id="@+id/tvAccountEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mailOfUser"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="8dp"
                android:paddingBottom="16dp">

                <TextView
                    android:id="@+id/tvVersionLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/version"
                    android:textSize="16sp"
                    android:textColor="@android:color/black"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <TextView
                    android:id="@+id/tvVersionValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/versionNumber"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>



            <Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"/>


           <Button
               android:id="@+id/btnLogout"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:text="@string/logout"
                android:backgroundTint="@color/selectedicon"
               android:textColor="@color/white"
               android:layout_gravity="end"
               android:layout_marginEnd="10dp"
               />




        </LinearLayout>
    </ScrollView>

</LinearLayout>