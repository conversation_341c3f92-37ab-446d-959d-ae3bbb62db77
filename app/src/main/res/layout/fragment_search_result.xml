<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:background="@android:color/white">

    <!-- Toolbar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <ImageButton
            android:id="@+id/btnBack"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Back" />

        <TextView
            android:id="@+id/tvSearchQuery"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="16sp"
            android:textColor="#333333"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            tools:text="Kết quả cho: example" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0" />

    <!-- Filter Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <RadioGroup
            android:id="@+id/radioGroupSort"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rbNewest"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:background="@drawable/tab_selector"
                android:textColor="@color/tab_text_selector"
                android:button="@null"
                android:layout_marginEnd="12dp"
                android:text="@string/post_search_newest"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rbPopular"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:background="@drawable/tab_selector"
                android:textColor="@color/tab_text_selector"
                android:button="@null"
                android:text="@string/post_search_popular" />
        </RadioGroup>

        <Button
            android:id="@+id/btnFilter"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/post_search_filter"
            android:textSize="12sp"
            android:padding="4dp"
            android:background="@drawable/tag_backround"
            android:textColor="@android:color/white"
            app:backgroundTint="@null" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E0E0E0" />

    <!-- Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewResults"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/post_item" />

        <TextView
            android:id="@+id/tvNoResults"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/post_search_no_result"
            android:textSize="16sp"
            android:gravity="center"
            android:padding="16dp"
            android:layout_gravity="center"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />
    </FrameLayout>
</LinearLayout>