<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardUseCompatPadding="false"
    app:contentPadding="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">


        <TextView
            android:id="@+id/tvDateLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/payment_date"
            android:textColor="@color/gray"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@id/tvAmount"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvDateLabel"
            tools:text="May 20, 2025" />


        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tvDate"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvDateLabel"
            tools:text="98,000 VND" />


        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="12dp"
            android:background="#E0E0E0"
            app:layout_constraintTop_toBottomOf="@id/tvDate" />


        <TextView
            android:id="@+id/tvSubscriptionLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/payment_subscription"
            android:textColor="@color/gray"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider" />

        <TextView
            android:id="@+id/tvSubscription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@id/tvStatus"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSubscriptionLabel"
            tools:text="Monthly Premium" />


        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_status_completed"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/tvSubscription"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvSubscriptionLabel"
            tools:text="Completed" />


        <TextView
            android:id="@+id/tvExpiryLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="@string/payment_expiry"
            android:textColor="@color/gray"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSubscription" />

        <TextView
            android:id="@+id/tvExpiry"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@id/tvRemaining"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvExpiryLabel"
            tools:text="June 20, 2025" />


        <TextView
            android:id="@+id/tvRemaining"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/green"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/tvExpiry"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvExpiryLabel"
            tools:text="30 days remaining" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>