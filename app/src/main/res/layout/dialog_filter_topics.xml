<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/post_filter_header"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <com.google.android.flexbox.FlexboxLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/flexboxTopics"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:flexWrap="wrap"
        app:alignItems="center"
        app:justifyContent="flex_start">
        <!-- Radio buttons sẽ được thêm vào động -->
    </com.google.android.flexbox.FlexboxLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btnReset"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/post_filter_reset"
            android:backgroundTint="#EEEEEE"
            android:textColor="#333333"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btnApply"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/post_filter_apply"
            android:backgroundTint="@color/selectedicon"
            android:textColor="@android:color/white" />
    </LinearLayout>
</LinearLayout>