<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    tools:context=".presentation.feature.menu.profile.ProfileActivity">


    <ImageView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_back_left"

        android:padding="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_profile_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/my_profile"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        app:layout_constraintTop_toTopOf="@id/btnBack"
        app:layout_constraintBottom_toBottomOf="@id/btnBack"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <androidx.cardview.widget.CardView
        android:id="@+id/cv_profile_picture_container"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="32dp"
        app:cardBackgroundColor="@color/selectedicon"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:layout_constraintTop_toBottomOf="@id/tv_profile_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    <TextView
        android:id="@+id/tv_change_picture"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/change"
        android:textColor="@android:color/white"
        android:textSize="16sp"/>

</androidx.cardview.widget.CardView>


    <TextView
        android:id="@+id/tv_username_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/username"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@id/cv_profile_picture_container"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/fl_username_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_username_label"
        android:background="@drawable/bg_profile_field_border"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    <TextView
        android:id="@+id/tv_username_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:text="@string/nameOfUser"
        android:textColor="@android:color/black"
        android:textSize="@dimen/text_medium"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right"
        android:layout_gravity="end|center_vertical" />
    </FrameLayout>


    <TextView
        android:id="@+id/tv_status_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/status_message"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/fl_username_container"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/fl_status_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_profile_field_border"
        android:padding="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_status_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    <TextView
        android:id="@+id/tv_status_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:text="@string/detail_status_message"
        android:textColor="@android:color/black"
        android:textSize="@dimen/text_medium"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right"
        android:layout_gravity="end|center_vertical"
        />
</FrameLayout>


    <TextView
        android:id="@+id/tv_student_info_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/student_information"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/fl_status_container"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/fl_student_info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_profile_field_border"
        android:padding="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_student_info_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

    <TextView
        android:id="@+id/tv_student_info_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:text="@string/detail_student_infomation"
        android:textColor="@android:color/black"
        android:textSize="@dimen/text_medium"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right"
        android:layout_gravity="end|center_vertical"
        />
</FrameLayout>

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@color/colorForDevider"
        android:layout_marginTop="32dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toBottomOf="@id/fl_student_info_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


    <TextView
        android:id="@+id/tv_account_info_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/account_information"
        android:textColor="@android:color/darker_gray"
        android:textSize="14sp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/divider"
        app:layout_constraintStart_toStartOf="parent" />


    <TextView
        android:id="@+id/tv_email_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/email"
        android:textColor="@android:color/black"
        android:textSize="@dimen/text_medium"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_account_info_label"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_email_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/mailOfUser"
        android:textColor="@color/unselectedicon"
        android:textSize="@dimen/text_small"
        android:gravity="end"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toTopOf="@id/tv_email_label"
        app:layout_constraintBottom_toBottomOf="@id/tv_email_label"
        app:layout_constraintStart_toEndOf="@id/tv_email_label"
        app:layout_constraintEnd_toEndOf="parent" />


    <TextView
        android:id="@+id/tv_uid_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snap_solve_uid"
        android:textColor="@android:color/black"
        android:textSize="@dimen/text_medium"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@id/tv_email_label"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_uid_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/user_id"
        android:textColor="@color/unselectedicon"
        android:textSize="@dimen/text_small"
        android:gravity="end"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toTopOf="@id/tv_uid_label"
        app:layout_constraintBottom_toBottomOf="@id/tv_uid_label"
        app:layout_constraintStart_toEndOf="@id/tv_uid_label"
        app:layout_constraintEnd_toEndOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>