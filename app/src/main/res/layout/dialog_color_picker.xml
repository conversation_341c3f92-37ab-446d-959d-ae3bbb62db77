<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_rounded_background"
    android:padding="20dp">

    <!-- Header với gradient -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/dialog_header_gradient"
        android:padding="16dp"
        android:layout_marginBottom="20dp"
        android:elevation="2dp"
        android:clipToOutline="true"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chọn màu"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-medium" />
    </LinearLayout>

    <!-- Color preview với viền và hiệu ứng shadow -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <View
            android:id="@+id/color_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black" />
    </androidx.cardview.widget.CardView>

    <!-- Predefined colors -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Màu cơ bản"
        android:textSize="17sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="12dp"
        android:fontFamily="sans-serif-medium" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/color_buttons_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:spacing="8dp" />
    </HorizontalScrollView>

    <!-- Custom color section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:contentPadding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tùy chỉnh màu"
                android:textSize="17sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="16dp"
                android:fontFamily="sans-serif-medium" />

            <!-- Red slider with improved design -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="R"
                    android:textSize="18sp"
                    android:textColor="#F44336"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium" />

                <SeekBar
                    android:id="@+id/seekbar_red"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:progressTint="#F44336"
                    android:thumbTint="#F44336"
                    android:progressBackgroundTint="#E0E0E0"
                    android:max="255"/>

                <TextView
                    android:id="@+id/text_red_value"
                    android:layout_width="45dp"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:fontFamily="sans-serif-medium" />
            </LinearLayout>

            <!-- Green slider with improved design -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="G"
                    android:textSize="18sp"
                    android:textColor="#4CAF50"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium" />

                <SeekBar
                    android:id="@+id/seekbar_green"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:progressTint="#4CAF50"
                    android:thumbTint="#4CAF50"
                    android:progressBackgroundTint="#E0E0E0"
                    android:max="255" />

                <TextView
                    android:id="@+id/text_green_value"
                    android:layout_width="45dp"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:fontFamily="sans-serif-medium" />
            </LinearLayout>

            <!-- Blue slider with improved design -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="B"
                    android:textSize="18sp"
                    android:textColor="#2196F3"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium" />

                <SeekBar
                    android:id="@+id/seekbar_blue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:progressTint="#2196F3"
                    android:thumbTint="#2196F3"
                    android:progressBackgroundTint="#E0E0E0"
                    android:max="255" />

                <TextView
                    android:id="@+id/text_blue_value"
                    android:layout_width="45dp"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:fontFamily="sans-serif-medium" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Buttons với thiết kế đẹp mắt -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="63dp"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <!-- Nút Hủy với thiết kế thanh lịch -->
        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/button_cancel_background"
            android:elevation="2dp"
            android:fontFamily="sans-serif-medium"
            android:stateListAnimator="@null"
            android:text="Hủy"
            android:textColor="#757575"
            android:textSize="16sp" />

        <!-- Nút OK với thiết kế nổi bật -->
        <Button
            android:id="@+id/btn_ok"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/button_ok_background"
            android:elevation="2dp"
            android:fontFamily="sans-serif-medium"
            android:stateListAnimator="@null"
            android:text="Áp dụng"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>