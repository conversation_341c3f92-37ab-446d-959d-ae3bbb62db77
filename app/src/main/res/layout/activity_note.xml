<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".presentation.feature.noteScene.NoteActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/white"
        android:orientation="horizontal"
        android:layout_marginTop="5dp"
        android:weightSum="10"
        android:id="@+id/element_top_note">

        <ImageButton
            android:id="@+id/button_back_note"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/white"
            android:contentDescription="@string/back"
            android:src="@drawable/back_img" />
        <TextView
            android:id="@+id/text_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:text="@string/noteTitle"
            android:textAlignment="center"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:lines="1"
            android:textSize="17sp"
            android:gravity="center"
            />

        <Button
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="3"
            android:layout_marginStart="12dp"
            android:paddingEnd="10dp"
            android:paddingStart="10dp"
            android:text="@string/day"
            android:textStyle="normal"
            android:background="@drawable/spinner_border_round"
            android:gravity="center"
            android:layout_gravity="center"
            android:id="@+id/note_button_type"
            android:drawableEnd="@drawable/arrow_down"
            android:drawableTint="@color/white"
            android:textColor="@color/white"
            android:textAllCaps="false"
            />

        <Button
            android:id="@+id/note_button_menu"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:textAllCaps="false"
            android:layout_marginEnd="8dp"
            android:layout_weight="3"
            android:layout_gravity="center"
            android:textStyle="normal"
            android:background="@drawable/spinner_border_round"
            android:drawableEnd="@drawable/arrow_down"
            android:drawableTint="@color/white"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/newbutton"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Path bar showing current folder path -->
    <LinearLayout
        android:id="@+id/path_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/white"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@+id/element_top_note">
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleViewNote"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/path_bar" />

    <!-- Progress indicator -->
    <!-- Ensure the ProgressBar is centered and visible -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:indeterminateTint="@color/selectedicon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Floating button for QR scanning -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_scan_qr"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@drawable/ic_qr_code_scanner"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@android:color/white"
        app:backgroundTint="@color/selectedicon" />

</androidx.constraintlayout.widget.ConstraintLayout>