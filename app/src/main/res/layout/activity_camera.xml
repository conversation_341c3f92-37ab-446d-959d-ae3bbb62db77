<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".presentation.feature.camera.CameraActivity">

    <!-- Camera Layout -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cameraLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/black"
            app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:src="@drawable/back_img"
                app:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Snap"
                android:textColor="@color/white"
                android:textSize="18sp" />

        </androidx.appcompat.widget.Toolbar>

        <androidx.camera.view.PreviewView
            android:id="@+id/cameraPreviewView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/selectedicon"
            app:layout_constraintBottom_toTopOf="@id/cameraControls"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <!-- Placeholder text area -->
            <TextView
                android:layout_width="200dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:background="@drawable/dotted_border"
                android:gravity="center"
                android:text="align with text"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </androidx.camera.view.PreviewView>

        <LinearLayout
            android:id="@+id/cameraControls"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@color/black"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageButton
                android:id="@+id/btnGallery"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_image_library"
                android:contentDescription="Gallery" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/btnCapture"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:backgroundTint="@color/white"
                android:contentDescription="Take photo"
                app:borderWidth="4dp"
                app:fabCustomSize="64dp"
                app:maxImageSize="48dp"
                app:tint="@color/black" />

            <View
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="16dp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Preview Layout -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/previewLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/previewToolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                android:id="@+id/btnBackPreview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:contentDescription="Back"
                android:src="@drawable/back_img" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Get image"
                android:textColor="@color/black"
                android:textSize="18sp" />

        </androidx.appcompat.widget.Toolbar>

        <FrameLayout
            android:id="@+id/previewContainer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/scan_button_bg"
            app:layout_constraintBottom_toTopOf="@id/previewControls"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/previewToolbar">

            <ImageView
                android:id="@+id/imagePreview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="Preview image"
                android:scaleType="centerInside" />

            <com.example.app_music.presentation.feature.camera.CropOverlayView
                android:id="@+id/cropOverlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />
        </FrameLayout>

        <LinearLayout
            android:id="@+id/previewControls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="horizontal"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <Button
                android:id="@+id/btnRetry"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/white"
                android:text="Again"
                android:textColor="@color/black"
                android:textAllCaps="false"/>

            <Button
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:text="Confirm"
                android:textColor="@color/white"
                android:textAllCaps="false"/>

            <Button
                android:id="@+id/btnRotate"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginHorizontal="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/white"
                android:text="Rotate"
                android:textColor="@color/black"
                android:textAllCaps="false"/>


        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>