<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission
        android:name="android.permission.CAMERA"
        tools:ignore="PermissionImpliesUnsupportedChromeOsHardware" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />

    <application
        android:name=".SnapSolveApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/iconapp"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@drawable/iconapp"
        android:supportsRtl="true"
        android:theme="@style/Theme.App_music"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".presentation.feature.detail_notification.NotificationDetailActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.notification.NotificationActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.menu.premiumuser.PremiumUser"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="premium"
                    android:scheme="snapsolve" />
            </intent-filter>
        </activity>
        <activity
            android:name=".presentation.feature.menu.transactions.TransactionHistoryActivity"
            android:exported="false" />
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name=".presentation.feature.qrscanner.QRScannerActivity"
            android:exported="false" /> <!-- Text Search Activity -->
        <activity
            android:name=".presentation.feature.textsearch.TextSearchActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.searchhistory.SearchHistoryActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.camera.CameraActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.camera.ResultActivity"
            android:exported="false" /> <!-- Existing Activities -->

        <activity
            android:name=".presentation.feature.translate.TranslateActivity"
            android:exported="false" />

        <!-- Existing Activities -->
        <activity
            android:name=".presentation.feature.auth.UpdateProfileActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.auth.RegisterActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.auth.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".presentation.feature.menu.setting.multilanguage.LanguageSettingsActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.menu.editstudentInformation.EditStudentInformation"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.menu.editstatusmessage.EditStatusMessageActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.menu.editusernamescreen.EditUserNameActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.menu.profile.ProfileActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.menu.setting.SettingActivity"
            android:exported="false" />
        <activity
            android:name=".presentation.feature.noteScene.NoteActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="folders"
                    android:scheme="snapsolve" />
            </intent-filter>
        </activity>
        <activity
            android:name=".presentation.feature.noteScene.NoteDetailActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="notes"
                    android:scheme="snapsolve" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:exported="false" />

        <!-- Add material design library to use chip components -->
        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />
    </application>

</manifest>